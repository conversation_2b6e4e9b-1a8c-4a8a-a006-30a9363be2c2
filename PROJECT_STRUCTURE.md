# InteractionCapture Pro - Project Structure

## 📁 Complete Project Directory Structure

```
interaction-capture-pro/
├── 📁 frontend/                          # React + TypeScript Frontend
│   ├── 📁 public/
│   │   ├── favicon.ico
│   │   ├── manifest.json
│   │   └── index.html
│   ├── 📁 src/
│   │   ├── 📁 components/                # Reusable UI Components
│   │   │   ├── 📁 ui/                   # Base UI Components
│   │   │   │   ├── Button.tsx
│   │   │   │   ├── Card.tsx
│   │   │   │   ├── Input.tsx
│   │   │   │   ├── Modal.tsx
│   │   │   │   ├── Table.tsx
│   │   │   │   ├── Chart.tsx
│   │   │   │   └── index.ts
│   │   │   ├── 📁 layout/               # Layout Components
│   │   │   │   ├── Sidebar.tsx
│   │   │   │   ├── Header.tsx
│   │   │   │   ├── Navigation.tsx
│   │   │   │   └── Footer.tsx
│   │   │   ├── 📁 recording/            # Recording Components
│   │   │   │   ├── RecordingInterface.tsx
│   │   │   │   ├── TaskGuidance.tsx
│   │   │   │   ├── ProgressIndicator.tsx
│   │   │   │   ├── RecordingControls.tsx
│   │   │   │   └── DataQualityMonitor.tsx
│   │   │   ├── 📁 analytics/            # Analytics Components
│   │   │   │   ├── MetricsOverview.tsx
│   │   │   │   ├── InteractionHeatmap.tsx
│   │   │   │   ├── BehaviorTimeline.tsx
│   │   │   │   ├── MousePatterns.tsx
│   │   │   │   └── TypingAnalysis.tsx
│   │   │   └── 📁 dashboard/            # Dashboard Components
│   │   │       ├── QuickActions.tsx
│   │   │       ├── LiveMetrics.tsx
│   │   │       ├── RecentSessions.tsx
│   │   │       └── SystemHealth.tsx
│   │   ├── 📁 pages/                    # Page Components
│   │   │   ├── Dashboard.tsx
│   │   │   ├── 📁 recording/
│   │   │   │   ├── NewSession.tsx
│   │   │   │   ├── ActiveRecording.tsx
│   │   │   │   └── SessionDetails.tsx
│   │   │   ├── 📁 analytics/
│   │   │   │   ├── Overview.tsx
│   │   │   │   ├── Sessions.tsx
│   │   │   │   ├── Heatmaps.tsx
│   │   │   │   └── Reports.tsx
│   │   │   ├── 📁 data/
│   │   │   │   ├── Export.tsx
│   │   │   │   ├── Import.tsx
│   │   │   │   └── Quality.tsx
│   │   │   └── 📁 settings/
│   │   │       ├── Recording.tsx
│   │   │       ├── Display.tsx
│   │   │       └── Account.tsx
│   │   ├── 📁 hooks/                    # Custom React Hooks
│   │   │   ├── useRecording.ts
│   │   │   ├── useAnalytics.ts
│   │   │   ├── useTheme.ts
│   │   │   ├── useWebSocket.ts
│   │   │   └── useLocalStorage.ts
│   │   ├── 📁 services/                 # API Services
│   │   │   ├── api.ts
│   │   │   ├── recording.ts
│   │   │   ├── analytics.ts
│   │   │   ├── sessions.ts
│   │   │   └── websocket.ts
│   │   ├── 📁 store/                    # State Management
│   │   │   ├── index.ts
│   │   │   ├── recordingStore.ts
│   │   │   ├── analyticsStore.ts
│   │   │   ├── sessionStore.ts
│   │   │   └── settingsStore.ts
│   │   ├── 📁 utils/                    # Utility Functions
│   │   │   ├── dataProcessing.ts
│   │   │   ├── compression.ts
│   │   │   ├── validation.ts
│   │   │   ├── formatting.ts
│   │   │   └── constants.ts
│   │   ├── 📁 types/                    # TypeScript Definitions
│   │   │   ├── recording.ts
│   │   │   ├── analytics.ts
│   │   │   ├── session.ts
│   │   │   └── api.ts
│   │   ├── 📁 styles/                   # Styling Files
│   │   │   ├── globals.css
│   │   │   ├── components.css
│   │   │   └── animations.css
│   │   ├── App.tsx
│   │   ├── main.tsx
│   │   └── vite-env.d.ts
│   ├── package.json
│   ├── tsconfig.json
│   ├── vite.config.ts
│   ├── tailwind.config.js
│   └── postcss.config.js
├── 📁 backend/                           # Node.js + Express Backend
│   ├── 📁 src/
│   │   ├── 📁 controllers/              # Route Controllers
│   │   │   ├── sessionController.ts
│   │   │   ├── recordingController.ts
│   │   │   ├── analyticsController.ts
│   │   │   └── dataController.ts
│   │   ├── 📁 services/                 # Business Logic
│   │   │   ├── recordingService.ts
│   │   │   ├── analyticsService.ts
│   │   │   ├── dataProcessingService.ts
│   │   │   ├── compressionService.ts
│   │   │   └── storageService.ts
│   │   ├── 📁 models/                   # Database Models
│   │   │   ├── Session.ts
│   │   │   ├── InteractionData.ts
│   │   │   ├── Analytics.ts
│   │   │   └── User.ts
│   │   ├── 📁 middleware/               # Express Middleware
│   │   │   ├── auth.ts
│   │   │   ├── validation.ts
│   │   │   ├── rateLimit.ts
│   │   │   └── errorHandler.ts
│   │   ├── 📁 routes/                   # API Routes
│   │   │   ├── sessions.ts
│   │   │   ├── recording.ts
│   │   │   ├── analytics.ts
│   │   │   └── data.ts
│   │   ├── 📁 utils/                    # Utility Functions
│   │   │   ├── database.ts
│   │   │   ├── logger.ts
│   │   │   ├── validation.ts
│   │   │   └── helpers.ts
│   │   ├── 📁 types/                    # TypeScript Definitions
│   │   │   ├── api.ts
│   │   │   ├── database.ts
│   │   │   └── recording.ts
│   │   ├── 📁 config/                   # Configuration Files
│   │   │   ├── database.ts
│   │   │   ├── redis.ts
│   │   │   ├── storage.ts
│   │   │   └── environment.ts
│   │   ├── app.ts
│   │   └── server.ts
│   ├── package.json
│   ├── tsconfig.json
│   └── nodemon.json
├── 📁 recording-engine/                  # High-Performance Recording Engine
│   ├── 📁 src/
│   │   ├── 📁 core/                     # Core Recording Logic
│   │   │   ├── InteractionRecorder.ts
│   │   │   ├── DataBuffer.ts
│   │   │   ├── CompressionEngine.ts
│   │   │   └── QualityAnalyzer.ts
│   │   ├── 📁 trackers/                 # Specific Trackers
│   │   │   ├── MouseTracker.ts
│   │   │   ├── KeyboardTracker.ts
│   │   │   ├── ScrollTracker.ts
│   │   │   ├── FocusTracker.ts
│   │   │   └── FormTracker.ts
│   │   ├── 📁 scenarios/                # Recording Scenarios
│   │   │   ├── EcommerceScenario.ts
│   │   │   ├── SocialMediaScenario.ts
│   │   │   ├── ProfessionalScenario.ts
│   │   │   └── EducationalScenario.ts
│   │   ├── 📁 utils/                    # Engine Utilities
│   │   │   ├── performance.ts
│   │   │   ├── validation.ts
│   │   │   └── helpers.ts
│   │   └── index.ts
│   ├── package.json
│   └── tsconfig.json
├── 📁 database/                          # Database Scripts and Migrations
│   ├── 📁 migrations/
│   │   ├── 001_initial_schema.sql
│   │   ├── 002_add_analytics_tables.sql
│   │   └── 003_add_indexes.sql
│   ├── 📁 seeds/
│   │   ├── sample_sessions.sql
│   │   └── test_data.sql
│   └── schema.sql
├── 📁 docker/                            # Docker Configuration
│   ├── Dockerfile.frontend
│   ├── Dockerfile.backend
│   ├── Dockerfile.nginx
│   └── docker-compose.yml
├── 📁 docs/                              # Documentation
│   ├── API.md
│   ├── DEPLOYMENT.md
│   ├── DEVELOPMENT.md
│   └── USER_GUIDE.md
├── 📁 scripts/                           # Build and Deployment Scripts
│   ├── build.sh
│   ├── deploy.sh
│   ├── test.sh
│   └── setup.sh
├── 📁 tests/                             # Test Files
│   ├── 📁 frontend/
│   ├── 📁 backend/
│   └── 📁 e2e/
├── .gitignore
├── README.md
├── LICENSE
└── package.json
```

## 🔧 Key Implementation Files

### Frontend Package.json
```json
{
  "name": "interaction-capture-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "zustand": "^4.3.6",
    "framer-motion": "^10.0.0",
    "d3": "^7.8.2",
    "chart.js": "^4.2.1",
    "react-chartjs-2": "^5.2.0",
    "socket.io-client": "^4.6.1",
    "axios": "^1.3.4",
    "date-fns": "^2.29.3",
    "react-hot-toast": "^2.4.0",
    "lucide-react": "^0.323.0",
    "clsx": "^1.2.1",
    "tailwind-merge": "^1.10.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.28",
    "@types/react-dom": "^18.0.11",
    "@types/d3": "^7.4.0",
    "@typescript-eslint/eslint-plugin": "^5.57.1",
    "@typescript-eslint/parser": "^5.57.1",
    "@vitejs/plugin-react": "^4.0.0",
    "autoprefixer": "^10.4.14",
    "eslint": "^8.38.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.3.4",
    "postcss": "^8.4.21",
    "tailwindcss": "^3.2.7",
    "typescript": "^5.0.2",
    "vite": "^4.3.2",
    "vitest": "^0.29.8"
  }
}
```

### Backend Package.json
```json
{
  "name": "interaction-capture-backend",
  "version": "1.0.0",
  "main": "dist/server.js",
  "scripts": {
    "dev": "nodemon src/server.ts",
    "build": "tsc",
    "start": "node dist/server.js",
    "test": "jest",
    "lint": "eslint src --ext .ts"
  },
  "dependencies": {
    "express": "^4.18.2",
    "socket.io": "^4.6.1",
    "pg": "^8.10.0",
    "redis": "^4.6.5",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "helmet": "^6.1.5",
    "cors": "^2.8.5",
    "compression": "^1.7.4",
    "express-rate-limit": "^6.7.0",
    "winston": "^3.8.2",
    "joi": "^17.9.1",
    "multer": "^1.4.5-lts.1",
    "aws-sdk": "^2.1345.0",
    "lz4": "^0.6.5"
  },
  "devDependencies": {
    "@types/express": "^4.17.17",
    "@types/node": "^18.15.11",
    "@types/pg": "^8.6.6",
    "@types/bcryptjs": "^2.4.2",
    "@types/jsonwebtoken": "^9.0.1",
    "@types/cors": "^2.8.13",
    "@types/compression": "^1.7.2",
    "@types/multer": "^1.4.7",
    "@types/jest": "^29.5.0",
    "@typescript-eslint/eslint-plugin": "^5.57.1",
    "@typescript-eslint/parser": "^5.57.1",
    "eslint": "^8.38.0",
    "jest": "^29.5.0",
    "nodemon": "^2.0.22",
    "ts-jest": "^29.1.0",
    "ts-node": "^10.9.1",
    "typescript": "^5.0.2"
  }
}
```
